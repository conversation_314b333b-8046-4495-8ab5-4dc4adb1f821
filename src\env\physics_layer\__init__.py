"""
Physics Layer - 物理仿真层

SPACE2卫星边缘计算仿真环境的物理建模模块。
实现轨道动力学、通信链路等核心物理组件。

Modules:
- orbital: 轨道动力学计算，卫星位置和可见性矩阵
- communication: 通信链路性能计算，支持5种链路类型

Architecture: Foundation Layer → Physics Layer → Environment Layer
符合CLAUDE.md设计原则: 解耦、模块化、无硬编码参数
"""

from .communication import (
    LinkState,
    LinkCalculator, 
    LinkStateManager,
    CommunicationManagerRefactored
)

__all__ = [
    'LinkState',
    'LinkCalculator',
    'LinkStateManager', 
    'CommunicationManagerRefactored'
]