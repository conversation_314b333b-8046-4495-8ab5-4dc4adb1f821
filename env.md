# SPACE-DMPO1 卫星边缘计算仿真环境 - 技术文档

## 📖 目录

1. [系统概述](#系统概述)
2. [架构设计](#架构设计)  
3. [核心模块详解](#核心模块详解)
4. [运行逻辑流程](#运行逻辑流程)
5. [数据结构说明](#数据结构说明)
6. [配置系统](#配置系统)
7. [部署与运行](#部署与运行)
8. [开发指南](#开发指南)
9. [故障排除](#故障排除)

---

## 系统概述

SPACE2是一个**LEO卫星星座边缘-云协同计算仿真平台**，基于PettingZoo框架支持多智能体强化学习算法。该环境模拟了72颗LEO卫星、420个地面用户终端和5个云计算中心的复杂交互系统。

### 核心特性

- **物理精确仿真**: 基于真实轨道动力学和通信物理模型
- **多智能体强化学习**: 支持MAPPO、DPPO等先进算法
- **混合仿真模式**: 宏观决策 + 微观离散迭代
- **边缘-云协同**: 完整的计算卸载和资源调度仿真
- **任务分割并行处理**: 支持大任务的智能分割和并行执行

### 系统规模

- **卫星数量**: 72颗LEO卫星 (高度550km)
- **地面终端**: 420个全球分布的用户终端
- **云计算中心**: 5个战略位置的云中心
- **仿真时长**: 1441个时隙，每个时隙5秒
- **任务类型**: 多种计算密集型任务，支持动态优先级

---

## 架构设计

### 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    PettingZoo 多智能体RL环境                  │
├─────────────────────────────────────────────────────────────┤
│  环境协调器 (EnvironmentOrchestrator)                        │
│  ├── 状态管理器 (StateManager)                               │
│  ├── 任务管理器 (TaskManager)                                │
│  ├── 奖励计算器 (RewardCalculator)                           │
│  └── 观测管理器 (ObservationManager)                         │
├─────────────────────────────────────────────────────────────┤
│  卫星节点层 (SatelliteNode)                                  │
│  ├── 能量管理器 (EnergyManager)                              │
│  ├── 资源管理器 (ResourceManager)                            │
│  ├── 任务调度器 (TaskScheduler)                              │
│  └── 通信管理器 (CommunicationManager)                       │
├─────────────────────────────────────────────────────────────┤
│  物理仿真层                                                   │
│  ├── 轨道更新器 (OrbitalUpdater)                             │
│  ├── 通信链路管理 (CommunicationManagerRefactored)           │
│  ├── 任务分割处理器 (TaskPartitionManager)                   │
│  └── 云服务器管理 (CloudServerManager)                       │
├─────────────────────────────────────────────────────────────┤
│  基础设施层                                                   │
│  ├── 时间管理器 (TimeManager)                                │
│  ├── 错误处理框架 (ErrorHandler)                             │
│  ├── 性能优化系统 (PerformanceOptimizer)                     │
│  └── 日志配置管理 (LoggingManager)                           │
└─────────────────────────────────────────────────────────────┘
```

### 分层设计原则

1. **表示层**: PettingZoo标准接口，对外提供RL环境
2. **业务逻辑层**: 环境组件协调器，管理仿真逻辑
3. **实体层**: 卫星节点和任务实体，封装业务对象
4. **物理仿真层**: 轨道、通信、任务处理的物理建模
5. **基础设施层**: 时间、错误处理、性能优化等通用服务

---

## 核心模块详解

### 1. 时间管理系统 (time_manager.py)

#### 作用
统一管理仿真时间和物理时间的转换，解决原系统时间处理不一致的问题。

#### 核心类
- **TimeManager**: 主时间管理器
- **TimeContext**: 时间上下文数据类

#### 关键功能
```python
# 获取时间上下文
time_context = time_manager.get_time_context(simulation_step)
print(f"仿真步数: {time_context.simulation_step}")
print(f"仿真时间: {time_context.simulation_time}s") 
print(f"物理时间: {time_context.physical_time}")
print(f"时隙时长: {time_context.timeslot_duration}s")
```

#### 使用场景
- 所有模块的时间同步
- 卫星轨道位置计算
- 任务截止时间管理
- 性能统计时间戳

### 2. 轨道更新系统 (orbital.py)

#### 作用
管理72颗LEO卫星的轨道动力学计算，提供精确的位置和可见性信息。

#### 数据来源
- **satellite_data72_1.csv**: 72颗卫星1441个时隙的轨道数据
  - 格式: satillite_ID,time_slot,time,lat,lon,light,state
  - 103,752条记录 (72卫星 × 1441时隙)
- **global_ground_stations.csv**: 420个地面用户终端位置数据
- **cloud_station.csv**: 5个云计算中心位置数据
	

#### 核心功能
```python
# 获取指定时间步的所有卫星状态
satellites = orbital_updater.get_satellites_at_time(time_step)

# 构建三种可见性矩阵
inter_sat_matrix = orbital_updater.build_visibility_matrix(satellites)
ground_matrix = orbital_updater.build_satellite_ground_visibility_matrix(satellites)
cloud_matrix = orbital_updater.build_satellite_cloud_visibility_matrix(satellites)
```

#### 计算逻辑
1. **坐标转换**: 经纬度 → ECEF坐标系（地心地固坐标系）
2. **距离计算**: 3D欧几里得距离计算
3. **可见性判断**: 基于距离阈值（卫星-卫星: 2000km, 卫星-地面: 1000km, 卫星-云: 1200km）
4. **向量化处理**: 使用NumPy向量化操作提升性能

#### 核心类
- **Satellite**: 卫星实体类，存储位置和状态信息
- **GroundStation**: 地面站实体类，存储位置和属性信息
- **OrbitalUpdater**: 轨道更新器主类，管理所有计算逻辑


### 3. 通信链路管理 (communication_refactored.py)

#### 作用
基于物理模型计算卫星间、卫星-地面、卫星-云的通信链路状态。

#### 物理模型
- **路径损耗**: 自由空间传播损耗公式
- **信号强度**: 发射功率 + 天线增益 - 路径损耗
- **信噪比**: 信号功率 / 噪声功率
- **数据速率**: Shannon容量公式 C = B × log₂(1 + SNR)

#### 核心组件
```python
# 链路计算器 - 单链路物理计算
calculator = LinkCalculator(config)
link_state = calculator.calculate_complete_link_state(source_id, target_id, distance, link_type)

# 链路状态管理器 - 批量链路管理和缓存
manager = LinkStateManager(config, time_manager)
all_links = manager.compute_link_states(satellites, orbital_updater, time_step)
```

#### 输出数据
每个链路包含以下信息：
- 距离 (km)
- 数据传输速率 (Mbps)
- 传输延迟 (ms)
- 传输能耗 (J)
- 信号强度 (dBm)
- 信噪比 (dB)
- 链路类型

### 4. 卫星节点系统 (satellite_node_refactored.py)

#### 架构重构
原来的1144行God Class被分解为5个专业管理器：

```python
class SatelliteNode:
    def __init__(self, satellite_id, config, time_manager):
        self.energy_manager = EnergyManager(config)        # 能量管理
        self.resource_manager = ResourceManager(config)    # 资源管理  
        self.task_scheduler = TaskScheduler(config)        # 任务调度
        self.communication_manager = CommunicationManager(config)  # 通信管理
```

#### 各管理器职责

**EnergyManager (能量管理器)**
- 太阳能充电计算
- 电池状态监控
- 功耗管理
- 能量预测

```python
# 能量状态更新
energy_manager.update_energy_state(time_context, is_illuminated)

# 能量消耗检查
if energy_manager.can_perform_task(required_energy):
    energy_manager.consume_energy(required_energy)
```

**ResourceManager (资源管理器)**
- CPU、内存、存储资源分配
- 资源预留和释放
- 使用率监控
- 资源冲突检测

```python
# 资源预留
success = resource_manager.reserve_resources(task_id, cpu_cores, memory_gb)

# 资源释放
resource_manager.release_resources(task_id)
```

**TaskScheduler (任务调度器)**
- 任务队列管理
- 优先级调度
- 执行状态跟踪
- 性能监控

```python
# 任务管理
task_scheduler.add_task(new_task)
next_task = task_scheduler.get_next_task()
completed_tasks = task_scheduler.update_running_tasks(time_context)
```

### 5. 任务管理系统 (task.py)

#### 任务生命周期
```
GENERATED → QUEUED → PROCESSING → TRANSFERRING → COMPLETED
                          ↓
                      RETRYING ← FAILED
```

#### 任务属性
```python
class Task:
    task_id: str                    # 唯一标识
    task_type: int                  # 任务类型 (1-5)
    data_size_mb: float            # 数据大小
    complexity_cycles: int          # 计算复杂度 (CPU周期)
    deadline: float                # 截止时间
    priority: int                  # 优先级 (1-5)
    source_location_id: int        # 来源地面站ID
```

#### 动态优先级计算
```python
# 优先级公式
Score(Ti, t_now) = w_p × f_p(Pi) + w_d × f_d(Di, t_now) - w_c × f_c(Si, Ci)

# w_p: 优先级权重, w_d: 截止时间权重, w_c: 成本权重
# Pi: 基础优先级, Di: 截止时间紧迫性, Si: 数据大小, Ci: 计算复杂度
```

### 6. 云服务器管理 (cloud_server.py)

#### 系统架构
- **5个云计算中心**: 分布在全球战略位置
- **20个任务队列**: 每个云中心4个处理队列
- **固定处理时间**: 每个任务0.2秒处理时间
- **并行处理**: 每个云中心最多5个任务同时处理

#### 处理流程
```python
# 云任务处理周期
1. 任务接收: 从卫星接收待处理任务
2. 队列分配: 分配到最短队列
3. 并行处理: 启动处理线程 (最多5个)
4. 结果返回: 处理完成后标记并准备返回
5. 卫星回传: 通过最近卫星回传结果
```

### 7. 环境组件系统 (environment_components.py)

#### 组件化架构
将原来2000+行的环境类分解为4个专业组件：

**StateManager (状态管理器)**
- 卫星状态同步
- 任务状态管理
- 系统状态快照
- 状态一致性检查

**TaskManager (任务管理器)**
- 任务加载和生成
- 任务分配策略
- 任务状态跟踪
- 任务性能统计

**RewardCalculator (奖励计算器)**
- 多维度奖励计算
- 性能指标评估
- 奖励归一化和平滑
- 自适应奖励权重

**ObservationManager (观测管理器)**
- 状态观测提取
- 观测空间归一化
- 动作掩码生成
- 观测空间定义

#### 协调器模式
```python
class EnvironmentOrchestrator:
    def execute_step(self, time_context, actions):
        # 1. 状态同步
        state_result = self.components['state_manager'].step(time_context)
        
        # 2. 任务管理  
        task_result = self.components['task_manager'].step(time_context)
        
        # 3. 卫星更新
        for satellite in self.satellites.values():
            satellite.step(time_context)
        
        # 4. 奖励计算
        reward_result = self.components['reward_calculator'].step(time_context)
        
        # 5. 观测收集
        obs_result = self.components['observation_manager'].step(time_context)
        
        return StepResult(observations, rewards, dones, infos, new_tasks, metrics)
```

### 8. 性能优化系统 (performance_optimizations.py)

#### 高级缓存管理
```python
# 多层缓存策略
- L1缓存: 内存哈希表，最快访问
- L2缓存: 基于LRU的淘汰策略  
- L3缓存: 持久化存储 (可选)

# 智能缓存失效
- 时间失效: TTL机制
- 容量失效: 基于内存压力
- 依赖失效: 基于数据依赖关系
```

#### 内存优化
```python
# 内存监控和优化
- 实时内存使用监控
- 垃圾回收优化
- NumPy数组内存优化
- 弱引用管理
```

#### 计算优化
```python
# 向量化计算
distances = ComputationOptimizer.vectorized_distance_matrix(positions1, positions2)
visibility = ComputationOptimizer.batch_visibility_check(distances, threshold)

# 并行矩阵运算
results = ComputationOptimizer.parallel_matrix_operations(matrices, operation, n_threads)
```

### 9. 错误处理框架 (error_handling.py)

#### 分层异常体系
```python
SpaceSimulationError (基类)
├── TimeManagementError (时间管理错误)
├── ResourceError (资源管理错误)
├── CommunicationError (通信错误)
├── TaskExecutionError (任务执行错误)
├── ConfigurationError (配置错误)
└── DataIntegrityError (数据完整性错误)
```

#### 多策略错误处理
```python
# 错误处理策略
- LogOnlyStrategy: 仅记录日志
- RetryStrategy: 自动重试机制  
- GracefulDegradationStrategy: 优雅降级
- CircuitBreakerStrategy: 熔断机制
```

### 10. 日志配置系统 (logging_config.py)

#### 多层级日志
```python
# 日志输出目标
- 控制台输出: 实时查看
- 文件轮转: space_simulation.log
- 错误文件: errors.log
- 性能文件: performance.log
```

#### 配置管理
```python
# 多层级配置合并
1. 默认配置 (代码内置)
2. 配置文件 (config.yaml)  
3. 环境变量 (SPACE_SIM_*)
4. 运行时动态配置
```

---

## 运行逻辑流程

### 主要执行流程

#### 1. 系统初始化
```python
def initialize_system():
    # Step 1: 初始化基础设施
    initialize_logging_and_config("src/env/config.yaml")
    config = get_config_manager().get_config()
    time_manager = create_time_manager_from_config(config)
    
    # Step 2: 初始化物理仿真层
    orbital_updater = OrbitalUpdater(time_manager=time_manager)
    comm_manager = CommunicationManagerRefactored(config, time_manager)
    cloud_manager = CloudServerManager(orbital_updater)
    
    # Step 3: 初始化卫星节点
    satellites = {}
    for i in range(36):
        satellite_id = f"Satellite{111 + i}"
        satellites[satellite_id] = create_satellite_node(satellite_id, config, time_manager)
    
    # Step 4: 初始化环境系统
    orchestrator = EnvironmentOrchestrator(config, time_manager)
    orchestrator.initialize_components()
    orchestrator.set_dependencies(satellites, orbital_updater, comm_manager)
    
    return orchestrator, time_manager
```

#### 2. 单步仿真流程
```python
def single_simulation_step(orchestrator, time_manager, current_step, actions=None):
    # Step 1: 获取时间上下文
    time_context = time_manager.get_time_context(current_step)
    
    # Step 2: 执行环境步骤
    step_result = orchestrator.execute_step(time_context, actions)
    
    # 详细的内部流程:
    # 2.1 状态同步
    #     - 轨道状态更新: 获取卫星位置、光照状态
    #     - 通信状态更新: 计算可见性矩阵、链路状态
    #     - 系统状态检查: 验证状态一致性
    
    # 2.2 任务管理
    #     - 加载新任务: 从任务生成器获取当前时隙任务
    #     - 任务分配: 根据可见性和资源情况分配任务
    #     - 任务状态更新: 更新正在执行的任务状态
    
    # 2.3 卫星节点更新
    for satellite_id, satellite in satellites.items():
        # 能量状态更新
        satellite.energy_manager.update_energy_state(time_context, is_illuminated)
        
        # 任务处理更新
        completed_tasks = satellite.task_scheduler.update_running_tasks(time_context)
        
        # 资源释放
        for task_id in completed_tasks:
            satellite.resource_manager.release_resources(task_id)
        
        # 启动新任务
        satellite._try_start_new_tasks(time_context)
    
    # 2.4 云服务器更新
    cloud_results = cloud_manager.step(current_step, time_context.timeslot_duration, satellites)
    
    # 2.5 奖励计算
    rewards = calculate_multi_objective_rewards(satellites, cloud_results)
    
    # 2.6 观测收集
    observations = collect_normalized_observations(satellites)
    infos = generate_action_masks_and_info(satellites)
    
    return {
        'observations': observations,
        'rewards': rewards, 
        'dones': {sat_id: False for sat_id in satellites.keys()},
        'infos': infos,
        'step_metrics': step_result.system_metrics
    }
```

#### 3. 强化学习训练流程
```python
def training_loop():
    # 创建PettingZoo环境
    env = parallel_env()  # 或 aec_env()
    observations, infos = env.reset()
    
    for episode in range(num_episodes):
        for step in range(max_steps):
            # 智能体动作选择
            actions = {}
            for agent in env.agents:
                valid_actions = infos[agent]['valid_actions']
                action = policy.select_action(observations[agent], valid_actions)
                actions[agent] = action
            
            # 环境执行
            observations, rewards, dones, infos = env.step(actions)
            
            # 经验存储
            replay_buffer.store(observations, actions, rewards, dones)
            
            # 策略更新
            if step % update_frequency == 0:
                policy.update(replay_buffer.sample())
            
            if all(dones.values()):
                break
        
        # Episode结束统计
        episode_metrics = env.get_episode_metrics()
        logger.info(f"Episode {episode}: {episode_metrics}")
    
    env.close()
```

### 动作空间和观测空间

#### 动作空间 (每个卫星智能体)
```python
action_space = spaces.Discrete(42)

# 动作编码:
# 0: 本地处理
# 1-36: 卸载到其他卫星 (Satellite111 - Satellite146)
# 37-41: 卸载到云中心 (cloud_1 - cloud_5)
```

#### 观测空间 (每个卫星智能体)
```python
observation_space = spaces.Box(low=0.0, high=1.0, shape=(8,), dtype=np.float32)

# 观测向量:
[
    battery_percent / 100.0,           # 电池电量百分比 [0,1]
    cpu_utilization / 100.0,          # CPU使用率 [0,1]  
    task_queue_size / 20.0,           # 任务队列大小 [0,1]
    neighbor_count / 10.0,            # 邻居卫星数量 [0,1]
    (latitude + 90) / 180.0,          # 纬度归一化 [0,1]
    (longitude + 180) / 360.0,        # 经度归一化 [0,1]
    illuminated,                      # 光照状态 [0,1]
    success_rate                      # 任务成功率 [0,1]
]
```

### 奖励函数设计

#### 多目标奖励函数
```python
def calculate_reward(satellite):
    reward = 0.0
    
    # 1. 任务完成奖励 (主要目标)
    task_reward = satellite.tasks_completed * 10.0
    
    # 2. 能量效率奖励
    battery_percent = satellite.energy_manager.battery_level_percent
    energy_reward = (battery_percent / 100.0) * 2.0
    
    # 3. 资源利用率奖励 (避免资源浪费和过载)
    cpu_util = satellite.resource_manager.cpu_utilization_percent
    optimal_util = 70.0  # 最优利用率
    util_penalty = abs(cpu_util - optimal_util) / 100.0
    util_reward = (1.0 - util_penalty) * 3.0
    
    # 4. 协作奖励 (鼓励卫星间协作)
    collaboration_reward = 0.0
    if satellite.tasks_forwarded > 0:
        collaboration_reward = satellite.tasks_forwarded * 1.0
    
    # 5. 延迟惩罚 (任务超时惩罚)
    timeout_penalty = satellite.tasks_timeout * (-5.0)
    
    # 总奖励
    total_reward = task_reward + energy_reward + util_reward + collaboration_reward + timeout_penalty
    
    return max(total_reward, -10.0)  # 限制最小奖励
```

---

## 数据结构说明

### 核心数据文件

#### 1. 卫星轨道数据 (satellite_data72_1.csv)
```csv
satillite_ID,time_slot,time,lat,lon,light,state
111,0,04:00:00,0.0,-16.922,False,True
112,0,04:00:00,33.483,15.261,True,True
...
```
- **72颗卫星** × **1441个时隙** = **103,752条记录**
- **时隙间隔**: 5秒（时隙0-1440）
- **坐标系**: WGS84地理坐标系
- **光照状态**: 布尔值，影响太阳能充电
- **状态**: 布尔值，卫星是否正常工作
- 数据格式：按时隙递增输出，每个时隙包含所有72颗卫星的状态

#### 2. 地面用户终端数据 (global_ground_stations.csv)
```csv
ID,Latitude,Longitude,RegionType,Size,PurposeType
1,-65,-180,Land,Medium,Normal
2,-65,-168,Land,Large,Industrial
3,45.2,102.3,Ocean,Small,DelaySensitive
...
```
- **420个终端**全球分布
- **RegionType**: Land/Ocean (影响任务生成率)
- **Size**: Small/Medium/Large (影响计算需求)
- **PurposeType**: Normal/Industrial/DelaySensitive (影响优先级)

#### 3. 任务生成数据 (task_generation_results.json)
```json
{
  "simulation_metadata": {
    "total_locations": 420,
    "total_timeslots": 1000,  
    "total_tasks_generated": 156789
  },
  "simulation_results": [
    {
      "timeslot": 0,
      "total_tasks": 157,
      "locations": [
        {
          "location_id": 1,
          "num_tasks": 3,
          "generated_tasks": [
            {
              "task_id": 1,
              "type_id": 2,
              "data_size_mb": 35.67,
              "complexity_cycles_per_bit": 200,
              "deadline_timestamp": 50.0,
              "priority": 3
            }
          ]
        }
      ]
    }
  ]
}
```

### 运行时数据结构

#### 时间上下文
```python
@dataclass
class TimeContext:
    simulation_step: int        # 仿真步数 (0-999)
    simulation_time: float      # 仿真时间 (秒)
    physical_time: datetime     # 物理时间
    timeslot_duration: float    # 时隙时长 (秒)
```

#### 卫星状态
```python
@dataclass  
class SatelliteState:
    # 位置信息
    latitude: float
    longitude: float
    altitude: float = 550.0  # km
    is_illuminated: bool
    
    # 能量状态
    battery_level_j: float
    battery_capacity_j: float = 3600.0
    solar_power_w: float = 200.0
    
    # 计算资源
    cpu_cores: int = 4
    available_cpu_cores: int
    memory_gb: float = 8.0
    available_memory_gb: float
    
    # 通信状态
    visible_neighbors: List[str]
    visible_ground_stations: List[str]
    link_capacities: Dict[str, float]
    
    # 任务状态  
    task_queue: List[Task]
    running_tasks: Dict[str, Task]
    completed_tasks: int
    failed_tasks: int
```

#### 链路状态
```python
@dataclass
class LinkState:
    source_id: str
    target_id: str
    distance_km: float
    data_rate_mbps: float
    transmission_delay_ms: float
    transmission_energy_j: float
    signal_strength_dbm: float
    snr_db: float
    link_type: str  # 'inter_satellite', 'satellite_to_ground', 'satellite_to_cloud'
```

#### 任务状态
```python
@dataclass
class Task:
    task_id: str
    task_type: int                 # 1-5
    data_size_mb: float
    complexity_cycles: int         # CPU周期数
    deadline: float               # 截止时间戳
    priority: int                 # 1-5
    source_location_id: int       # 来源地面站
    
    # 动态状态
    current_state: TaskState      # GENERATED/QUEUED/PROCESSING/...
    processing_progress: float    # 处理进度 [0.0, 1.0]
    energy_consumed: float        # 已消耗能量
    processing_history: List[ProcessingRecord]
```

---

## 配置系统

### 主配置文件 (config.yaml)

```yaml
# 系统基础参数
system:
  timeslot_duration_s: 5               # 时隙持续时间
  total_timeslots: 1441                # 总时隙数
  num_leo_satellites: 72               # LEO卫星数量
  num_users: 420                       # 地面用户数量
  earth_radius_m: 6371000              # 地球半径
  leo_altitude_m: 550000               # LEO高度
  visibility_threshold_m: 2000000      # 可见性阈值
  simulation_start_time: "2025-06-08 04:00:00"

# 通信模型参数
communication:
  frequency_ghz: 28.0                  # 载波频率
  transmit_power_w: 1.0                # 发射功率
  antenna_gain_db: 20.0                # 天线增益
  bandwidth_mhz: 100.0                 # 信道带宽
  noise_temperature_k: 300.0           # 噪声温度
  receiver_sensitivity_dbm: -90.0      # 接收机灵敏度
  cache_duration_steps: 5              # 链路状态缓存时长

# 能量模型参数
energy:
  battery_capacity_j: 3600             # 电池容量
  solar_power_w: 200                   # 太阳能功率
  base_power_w: 50                     # 基础功耗
  processing_power_w: 100              # 处理功耗
  communication_power_w: 10            # 通信功耗

# 计算资源参数
computation:
  cpu_cores: 4                         # CPU核心数
  cpu_frequency_ghz: 2.5               # CPU主频
  memory_gb: 8.0                       # 内存大小
  storage_gb: 64.0                     # 存储大小
  cycles_per_bit: 1000                 # 每比特计算周期

# 任务模型参数
tasks:
  task_types: 5                        # 任务类型数量
  priority_levels: 5                   # 优先级等级
  complexity_min: 1000000              # 最小复杂度(周期)
  complexity_max: 100000000            # 最大复杂度(周期)
  size_min_mb: 1.0                     # 最小数据大小
  size_max_mb: 100.0                   # 最大数据大小
  deadline_factor: 3.0                 # 截止时间因子

# 奖励函数参数
reward:
  task_completion_reward: 10.0         # 任务完成奖励
  energy_efficiency_weight: 0.1        # 能量效率权重
  resource_utilization_weight: 5.0     # 资源利用权重
  collaboration_reward: 1.0            # 协作奖励
  timeout_penalty: -5.0                # 超时惩罚

# 强化学习参数
reinforcement_learning:
  algorithm: "MAPPO"                   # 算法类型
  learning_rate: 0.0003                # 学习率
  batch_size: 256                      # 批次大小
  buffer_size: 100000                  # 经验回放缓冲区
  update_frequency: 10                 # 更新频率
  target_update_frequency: 100         # 目标网络更新频率

# 性能优化参数
performance:
  cache_max_memory_mb: 512             # 缓存最大内存
  enable_performance_monitoring: true  # 启用性能监控
  gc_threshold_mb: 1000                # 垃圾回收阈值
  parallel_workers: 4                  # 并行工作线程

# 日志配置
logging:
  level: "INFO"                        # 日志级别
  directory: "logs"                    # 日志目录
  console_output: true                 # 控制台输出
  file_output: true                    # 文件输出
  max_file_size_mb: 10                 # 最大文件大小
  backup_count: 5                      # 备份文件数量
```

### 环境变量支持

```bash
# 覆盖配置的环境变量 (前缀: SPACE_SIM_)
export SPACE_SIM_SYSTEM_TOTAL_TIMESLOTS=500
export SPACE_SIM_LOGGING_LEVEL=DEBUG
export SPACE_SIM_PERFORMANCE_CACHE_MAX_MEMORY_MB=256
```

### 动态配置更新

```python
# 运行时配置更新
config_manager = get_config_manager()
config_manager.set_config('system.total_timeslots', 500)
config_manager.set_config('logging.level', 'DEBUG')

# 保存配置
config_manager.save_config('updated_config.yaml')
```

---

## 部署与运行

### 环境要求

#### Python环境
```bash
Python >= 3.8
numpy >= 1.20.0
pandas >= 1.3.0
pyyaml >= 5.4.0
pettingzoo >= 1.18.0
gymnasium >= 0.26.0
psutil >= 5.8.0
```

#### 系统要求
- **内存**: 最小4GB，推荐8GB+
- **存储**: 最小2GB可用空间
- **CPU**: 支持多核并行计算
- **操作系统**: Windows/Linux/macOS

### 安装步骤

#### 1. 克隆项目
```bash
git clone <repository_url>
cd SPACE-DMPO1
```

#### 2. 创建虚拟环境
```bash
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或
venv\Scripts\activate     # Windows
```

#### 3. 安装依赖
```bash
pip install -r requirements.txt
```

#### 4. 初始化系统
```python
# 首次运行初始化
python src/env/logging_config.py  # 测试日志系统
python src/env/time_manager.py    # 测试时间管理
python src/env/orbital_updater.py # 测试轨道计算
```

### 基础使用示例

#### 1. 简单仿真运行
```python
from src.env.logging_config import initialize_logging_and_config, get_config
from src.env.time_manager import create_time_manager_from_config
from src.env.orbital_updater import OrbitalUpdater
from src.env.satellite_node_refactored import create_satellite_node

# 初始化系统
initialize_logging_and_config()
config = get_config()
time_manager = create_time_manager_from_config(config)

# 创建核心组件
orbital_updater = OrbitalUpdater(time_manager=time_manager)
satellites = {}

# 创建卫星节点
for i in range(3):  # 简化示例，只创建3颗卫星
    satellite_id = f"Satellite{111 + i}"
    satellites[satellite_id] = create_satellite_node(satellite_id, config, time_manager)

# 运行仿真
for step in range(10):  # 运行10个时隙
    print(f"\n=== 时隙 {step} ===")
    
    # 获取时间上下文
    time_context = time_manager.get_time_context(step)
    
    # 更新轨道状态
    orbital_satellites = orbital_updater.get_satellites_at_time(step)
    
    # 同步卫星状态
    for satellite_id, satellite in satellites.items():
        if satellite_id in orbital_satellites:
            satellite.sync_with_orbital_state(orbital_satellites[satellite_id])
    
    # 执行卫星更新
    for satellite in satellites.values():
        satellite.step(time_context)
    
    # 打印状态
    for satellite_id, satellite in satellites.items():
        status = satellite.get_status_summary()
        print(f"{satellite_id}: {status}")

print("仿真完成")
```

#### 2. PettingZoo环境使用
```python
# 使用重构后的环境 (需要完整集成)
from src.env.satellite_env import parallel_env

# 创建环境
env = parallel_env()
observations, infos = env.reset()

print(f"智能体数量: {len(env.agents)}")
print(f"动作空间: {env.action_space(env.agents[0])}")
print(f"观测空间: {env.observation_space(env.agents[0])}")

# 随机策略测试
for step in range(100):
    actions = {}
    for agent in env.agents:
        # 使用动作掩码选择有效动作
        valid_actions = infos[agent]['valid_actions']
        actions[agent] = env.action_space(agent).sample()
        if actions[agent] not in valid_actions:
            actions[agent] = valid_actions[0] if valid_actions else 0
    
    observations, rewards, dones, infos = env.step(actions)
    
    if step % 10 == 0:
        avg_reward = sum(rewards.values()) / len(rewards)
        print(f"步骤 {step}: 平均奖励 = {avg_reward:.2f}")
    
    if all(dones.values()):
        print("Episode 结束")
        break

env.close()
```

#### 3. 性能监控和优化
```python
from src.env.performance_optimizations import (
    get_cache_manager, get_memory_optimizer, 
    optimize_system_performance, get_performance_profiler
)

# 系统运行中监控性能
def monitor_system_performance():
    # 获取缓存统计
    cache_stats = get_cache_manager().get_cache_stats()
    print(f"缓存统计: {cache_stats}")
    
    # 获取内存使用
    memory_info = get_memory_optimizer().check_memory_usage()
    print(f"内存使用: {memory_info}")
    
    # 系统优化
    optimization_result = optimize_system_performance()
    print(f"优化结果: {optimization_result}")

# 性能分析
profiler = get_performance_profiler()
profiler.start_profile("simulation_step")

# ... 执行仿真逻辑 ...

result = profiler.end_profile("simulation_step")
print(f"执行时间: {result['duration_s']:.3f}s, 内存变化: {result['memory_delta_mb']:.2f}MB")
```

### 高级配置

#### 1. 自定义配置文件
```python
# 使用自定义配置
initialize_logging_and_config("my_custom_config.yaml")
```

#### 2. 模块化启动
```python
# 只启用需要的模块
from src.env.orbital_updater import OrbitalUpdater
from src.env.communication_refactored import CommunicationManagerRefactored

# 创建最小系统
orbital_updater = OrbitalUpdater()
comm_manager = CommunicationManagerRefactored(config, time_manager)
comm_manager.set_orbital_updater(orbital_updater)

# 获取链路状态
link_states = comm_manager.get_all_link_states(time_step=0)
```

#### 3. 性能调优
```python
# 启用高性能模式
config = {
    'performance': {
        'cache_max_memory_mb': 1024,      # 增大缓存
        'enable_performance_monitoring': True,
        'parallel_workers': 8             # 增加并行线程
    },
    'logging': {
        'level': 'WARNING'                # 减少日志输出
    }
}
```

---

## 开发指南

### 开发环境设置

#### 1. 开发工具推荐
```bash
# IDE推荐
VS Code + Python扩展
PyCharm Professional

# 代码质量工具
pip install black flake8 mypy pytest pytest-cov

# 格式化代码
black src/
flake8 src/
mypy src/
```

#### 2. Git工作流
```bash
# 开发新功能
git checkout -b feature/new-feature
git commit -m "feat: add new feature"
git push origin feature/new-feature

# 提交前检查
python -m pytest tests/
python -m black src/
python -m flake8 src/
```

### 添加新功能

#### 1. 创建新模块模板
```python
"""
新模块模板
遵循项目架构和编码规范
"""
from typing import Dict, List, Any, Optional
import logging

from .time_manager import TimeManager, TimeContext
from .error_handling import handle_errors, SpaceSimulationError
from .logging_config import get_logger
from .performance_optimizations import cached_method, performance_monitor

logger = get_logger(__name__)


class NewFeatureManager:
    """新功能管理器 - 遵循单一职责原则"""
    
    def __init__(self, config: Dict, time_manager: TimeManager):
        self.config = config
        self.time_manager = time_manager
        self.is_initialized = False
        logger.info(f"{self.__class__.__name__} 初始化开始")
        
    def initialize(self):
        """初始化新功能"""
        try:
            # 初始化逻辑
            self.is_initialized = True
            logger.info("新功能初始化完成")
        except Exception as e:
            logger.error(f"新功能初始化失败: {e}")
            raise
    
    @handle_errors(module="new_feature", function="process_data")
    @performance_monitor
    @cached_method(cache_name="new_feature_cache", ttl=600)
    def process_data(self, data: Any) -> Dict[str, Any]:
        """处理数据的核心方法"""
        if not self.is_initialized:
            raise SpaceSimulationError("新功能未初始化", "NOT_INITIALIZED")
        
        logger.debug("开始处理数据")
        
        # 处理逻辑
        result = self._internal_processing(data)
        
        logger.info(f"数据处理完成: {result}")
        return result
    
    def _internal_processing(self, data: Any) -> Dict[str, Any]:
        """内部处理逻辑"""
        # 具体实现
        return {"status": "processed", "data": data}
    
    def get_status(self) -> Dict[str, Any]:
        """获取状态信息"""
        return {
            "initialized": self.is_initialized,
            "module": self.__class__.__name__
        }
```

#### 2. 单元测试模板
```python
"""
测试模板
"""
import unittest
from unittest.mock import Mock, patch
import sys
import os

# 添加src路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.env.new_feature_manager import NewFeatureManager
from src.env.time_manager import TimeManager


class TestNewFeatureManager(unittest.TestCase):
    """新功能管理器测试"""
    
    def setUp(self):
        """测试设置"""
        self.config = {
            'test_param': 'test_value'
        }
        self.time_manager = Mock(spec=TimeManager)
        self.manager = NewFeatureManager(self.config, self.time_manager)
    
    def test_initialization(self):
        """测试初始化"""
        self.assertFalse(self.manager.is_initialized)
        self.manager.initialize()
        self.assertTrue(self.manager.is_initialized)
    
    def test_process_data(self):
        """测试数据处理"""
        self.manager.initialize()
        test_data = {"input": "test"}
        
        result = self.manager.process_data(test_data)
        
        self.assertIn("status", result)
        self.assertEqual(result["status"], "processed")
    
    def test_error_handling(self):
        """测试错误处理"""
        # 未初始化时应该抛出异常
        with self.assertRaises(SpaceSimulationError):
            self.manager.process_data({"test": "data"})
    
    def tearDown(self):
        """测试清理"""
        pass


if __name__ == '__main__':
    unittest.main()
```

### 代码规范

#### 1. 命名约定
```python
# 类名：PascalCase
class SatelliteManager:
    pass

# 函数和变量名：snake_case
def calculate_distance():
    satellite_count = 36

# 常量：UPPER_SNAKE_CASE
MAX_SATELLITES = 36
DEFAULT_TIMESLOT_DURATION = 10

# 私有方法：前缀下划线
def _internal_method(self):
    pass
```

#### 2. 文档字符串
```python
def complex_function(param1: int, param2: str, param3: Optional[Dict] = None) -> List[str]:
    """
    复杂函数的文档字符串示例
    
    Args:
        param1: 第一个参数的描述
        param2: 第二个参数的描述  
        param3: 可选的第三个参数
        
    Returns:
        List[str]: 返回值的描述
        
    Raises:
        ValueError: 参数无效时抛出
        SpaceSimulationError: 仿真相关错误
        
    Examples:
        >>> result = complex_function(42, "test")
        >>> print(result)
        ['processed', 'test']
    """
    pass
```

#### 3. 错误处理
```python
# ✅ 推荐做法
@handle_errors(module="satellite", function="process_task")
def process_task(self, task: Task) -> bool:
    if task is None:
        raise_error(TaskExecutionError, "任务不能为空", "NULL_TASK")
    
    try:
        # 处理逻辑
        return self._internal_processing(task)
    except ResourceError as e:
        logger.warning(f"资源不足，任务推迟: {e}")
        return False

# ❌ 避免的做法
def process_task(self, task):
    try:
        # 处理逻辑
        pass
    except Exception as e:
        print(f"Error: {e}")  # 不要用print
        pass  # 不要忽略异常
```

### 性能优化指南

#### 1. 缓存策略
```python
# 计算密集型方法使用缓存
@cached_method(cache_name="distance_cache", ttl=300)
def calculate_distance_matrix(self, positions):
    # 昂贵的计算
    return expensive_computation(positions)

# 查看缓存效果
cache_stats = get_cache_manager().get_cache_stats()
hit_rate = cache_stats['distance_cache']['hit_rate']
if hit_rate < 0.5:
    logger.warning(f"缓存命中率过低: {hit_rate:.2%}")
```

#### 2. 内存管理
```python
# 注册大对象用于监控
memory_optimizer = get_memory_optimizer()
memory_optimizer.register_for_cleanup(large_data_structure)

# 定期检查内存使用
if step % 100 == 0:
    memory_info = memory_optimizer.check_memory_usage()
    if memory_info['rss_mb'] > 1000:  # 超过1GB
        memory_optimizer.force_garbage_collection()
```

#### 3. 并行计算
```python
# 使用向量化计算
from src.env.performance_optimizations import ComputationOptimizer

# 并行矩阵运算
matrices = [matrix1, matrix2, matrix3]
results = ComputationOptimizer.parallel_matrix_operations(
    matrices, lambda m: expensive_operation(m), n_threads=4
)
```

---

## 故障排除

### 常见问题及解决方案

#### 1. 初始化失败
```
错误: 致命错误：加载配置文件失败: src/env/config.yaml
```

**解决方案:**
```python
# 检查配置文件路径
import os
config_path = "src/env/config.yaml"
if not os.path.exists(config_path):
    print(f"配置文件不存在: {config_path}")
    
# 使用默认配置
from src.env.logging_config import initialize_logging_and_config
initialize_logging_and_config()  # 不传参数使用默认配置
```

#### 2. 内存使用过高
```
错误: 缓存内存使用超限 (1024.0MB), 开始清理
```

**解决方案:**
```python
# 减少缓存内存限制
config = {
    'performance': {
        'cache_max_memory_mb': 256  # 减少到256MB
    }
}

# 或手动清理缓存
from src.env.performance_optimizations import optimize_system_performance
optimization_result = optimize_system_performance()
```

#### 3. 时间步数超出范围
```
错误: 时间步 1000 超出有效范围 [0, 999]
```

**解决方案:**
```python
# 检查时间步范围
time_manager = get_time_manager()
if time_step >= time_manager.total_timeslots:
    time_step = time_manager.total_timeslots - 1

# 或扩展仿真时长
config_manager.set_config('system.total_timeslots', 2000)
```

#### 4. 卫星数据文件缺失
```
错误: 加载卫星数据失败: src/env/satellite_processed_data1.csv
```

**解决方案:**
```python
# 检查数据文件
data_files = [
    "src/env/satellite_processed_data1.csv",
    "src/env/updated_global_ground_stations.csv",
    "src/env/cloud_station.csv"
]

for file_path in data_files:
    if not os.path.exists(file_path):
        print(f"缺失数据文件: {file_path}")
        
# 生成模拟数据 (仅用于测试)
# ... 数据生成代码 ...
```

### 调试工具

#### 1. 日志分析
```python
# 查看日志文件
tail -f logs/space_simulation.log    # Linux/macOS
Get-Content -Wait logs/space_simulation.log  # Windows PowerShell

# 过滤错误日志
grep ERROR logs/space_simulation.log
```

#### 2. 性能监控
```python
from src.env.performance_optimizations import get_performance_profiler

# 开启性能分析
profiler = get_performance_profiler()
profiler.start_profile("full_simulation")

# ... 运行仿真 ...

result = profiler.end_profile("full_simulation")
print(f"总执行时间: {result['duration_s']:.2f}s")

# 查看所有性能分析
summary = profiler.get_profile_summary()
for profile_name, stats in summary.items():
    print(f"{profile_name}: 平均 {stats['avg_duration_s']:.3f}s")
```

#### 3. 系统状态检查
```python
def system_health_check():
    """系统健康检查"""
    health_report = {}
    
    # 检查缓存状态
    cache_stats = get_cache_manager().get_cache_stats()
    health_report['cache'] = {
        'total_caches': cache_stats.get('_summary', {}).get('total_caches', 0),
        'memory_usage_mb': cache_stats.get('_summary', {}).get('total_size_mb', 0)
    }
    
    # 检查内存使用
    memory_info = get_memory_optimizer().check_memory_usage()
    health_report['memory'] = {
        'usage_mb': memory_info['rss_mb'],
        'usage_percent': memory_info['percent']
    }
    
    # 检查错误统计
    error_stats = get_error_handler().get_error_stats()
    health_report['errors'] = {
        'total_errors': error_stats['total_errors'],
        'recent_errors': error_stats['recent_errors']
    }
    
    return health_report

# 使用健康检查
health = system_health_check()
print(f"系统健康状态: {health}")
```

### 性能调优建议

#### 1. 仿真规模调整
```python
# 小规模测试配置
test_config = {
    'system': {
        'total_timeslots': 100,        # 减少到100个时隙
        'num_leo_satellites': 18       # 减少到18颗卫星
    }
}

# 渐进式扩展
# 100 timeslots → 500 timeslots → 1441 timeslots
# 18 satellites → 36 satellites → 72 satellites
```

#### 2. 缓存优化
```python
# 针对不同数据设置不同TTL
cache_config = {
    'orbital_positions': 60,      # 轨道位置变化较慢
    'link_states': 30,           # 链路状态变化中等
    'task_assignments': 10       # 任务分配变化较快
}
```

#### 3. 并行计算优化
```python
# 根据CPU核心数调整并行度
import psutil
cpu_count = psutil.cpu_count()
optimal_workers = min(cpu_count - 1, 8)  # 保留一个核心，最多8个工作线程

config = {
    'performance': {
        'parallel_workers': optimal_workers
    }
}
```

---

## 总结

SPACE2是一个功能完整、架构合理的LEO卫星边缘计算仿真平台。通过本技术文档，开发者可以：

1. **理解系统架构** - 掌握各模块的职责和交互关系
2. **快速上手开发** - 使用提供的模板和规范进行开发
3. **高效部署运行** - 按照部署指南快速搭建环境
4. **优化系统性能** - 使用性能监控和优化工具
5. **排除常见故障** - 参考故障排除指南解决问题

该系统支持多种使用场景，从简单的轨道仿真到复杂的多智能体强化学习训练，都能提供稳定可靠的支持。

---

**文档版本**: v2.0  
**更新日期**: 2025-01-21  
**维护团队**: SPACE2开发组  

## 更新记录

### v2.0 (2025-01-21)
- 更新为72颗卫星配置（原36颗）
- 数据源改为satellite_data72_1.csv（103,752条记录）  
- 优化orbital.py模块，支持三种可见性矩阵计算
- 更新系统规模参数：1441个时隙，5秒间隔
- 完善向量化计算和ECEF坐标系支持