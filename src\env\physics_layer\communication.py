"""
重构后的通信管理器
基于orbital.py已计算的可见性矩阵和距离矩阵，专注于链路性能计算
简化实现，控制代码行数
"""
import numpy as np
import math
import logging
import os
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass

from .error_handling import (
    handle_errors, CommunicationError, ConfigurationError, 
    raise_error, safe_execute
)
from .time_manager import TimeManager

logger = logging.getLogger(__name__)


@dataclass
class LinkState:
    """链路状态数据类"""
    source_id: str
    target_id: str
    distance_km: float
    data_rate_mbps: float
    transmission_delay_ms: float
    transmission_energy_j: float
    signal_strength_dbm: float
    snr_db: float
    link_type: str


class LinkCalculator:
    """
    链路计算器 - 专门处理单个链路的物理计算
    基于射频通信理论计算链路性能指标
    """
    
    def __init__(self, config: Dict):
        self.config = config
        self._initialize_parameters()
        
    def _initialize_parameters(self):
        """从配置初始化物理参数"""
        comm_config = self.config.get('communication', {})
        
        # 射频参数
        self.frequency_ghz = comm_config.get('frequency_ghz', 28.0)
        self.frequency_hz = self.frequency_ghz * 1e9
        self.transmit_power_w = comm_config.get('transmit_power_w', 1.0)
        self.transmit_power_dbm = 10 * math.log10(self.transmit_power_w * 1000)
        self.antenna_gain_db = comm_config.get('antenna_gain_db', 20.0)
        self.bandwidth_mhz = comm_config.get('bandwidth_mhz', 100.0)
        self.bandwidth_hz = self.bandwidth_mhz * 1e6
        self.noise_temperature_k = comm_config.get('noise_temperature_k', 300.0)
        
        # 物理常数
        self.boltzmann_constant = 1.38e-23
        self.light_speed_km_ms = 300.0  # km/ms
        
        logger.debug("链路计算器参数初始化完成")
    
    @handle_errors(module="communication", function="calculate_path_loss")
    def calculate_path_loss(self, distance_km: float) -> float:
        """计算自由空间路径损耗 (dB)"""
        if distance_km <= 0:
            raise_error(CommunicationError, f"无效距离: {distance_km}", "INVALID_DISTANCE")
        
        distance_m = distance_km * 1000
        # FSPL = 20*log10(d) + 20*log10(f) + 20*log10(4π/c)
        path_loss_db = (20 * math.log10(distance_m) + 
                       20 * math.log10(self.frequency_hz) + 
                       20 * math.log10(4 * math.pi / 3e8))
        return path_loss_db
    
    def calculate_signal_strength(self, distance_km: float) -> float:
        """计算接收信号强度 (dBm)"""
        path_loss_db = self.calculate_path_loss(distance_km)
        # P_rx = P_tx + G_tx + G_rx - L_path
        signal_strength_dbm = (self.transmit_power_dbm + 
                              2 * self.antenna_gain_db - 
                              path_loss_db)
        return signal_strength_dbm
    
    def calculate_snr(self, signal_strength_dbm: float) -> float:
        """计算信噪比 (dB)"""
        # 噪声功率 = k * T * B
        noise_power_w = self.boltzmann_constant * self.noise_temperature_k * self.bandwidth_hz
        noise_power_dbm = 10 * math.log10(noise_power_w * 1000)
        return signal_strength_dbm - noise_power_dbm
    
    def calculate_data_rate(self, snr_db: float) -> float:
        """基于香农定理计算数据传输速率 (Mbps)"""
        if snr_db < -10:  # 信噪比过低
            return 0.0
        
        snr_linear = 10 ** (snr_db / 10)
        # C = B * log2(1 + SNR)
        capacity_bps = self.bandwidth_hz * math.log2(1 + snr_linear)
        return capacity_bps / 1e6
    
    def calculate_transmission_delay(self, distance_km: float) -> float:
        """计算传输延迟 (ms)"""
        # 光速传播延迟 + 处理延迟
        propagation_delay_ms = distance_km / self.light_speed_km_ms
        processing_delay_ms = 1.0
        return propagation_delay_ms + processing_delay_ms
    
    def calculate_transmission_energy(self, data_rate_mbps: float) -> float:
        """计算传输能耗 (J)"""
        # 简化能耗模型：基础功率 + 速率相关功率
        base_energy = self.transmit_power_w * 1.0  # 1秒基础能耗
        rate_factor = 1 + (data_rate_mbps / 1000.0)
        return base_energy * rate_factor
    
    def calculate_complete_link_state(self, source_id: str, target_id: str, 
                                    distance_km: float, link_type: str) -> LinkState:
        """计算完整的链路状态"""
        signal_strength = self.calculate_signal_strength(distance_km)
        snr = self.calculate_snr(signal_strength)
        data_rate = self.calculate_data_rate(snr)
        delay = self.calculate_transmission_delay(distance_km)
        energy = self.calculate_transmission_energy(data_rate)
        
        return LinkState(
            source_id=source_id,
            target_id=target_id,
            distance_km=distance_km,
            data_rate_mbps=data_rate,
            transmission_delay_ms=delay,
            transmission_energy_j=energy,
            signal_strength_dbm=signal_strength,
            snr_db=snr,
            link_type=link_type
        )


class LinkStateManager:
    """
    链路状态管理器 - 基于orbital.py的矩阵数据批量计算链路状态
    """
    
    def __init__(self, config: Dict, time_manager: TimeManager):
        self.config = config
        self.time_manager = time_manager
        self.calculator = LinkCalculator(config)
        
        # 简单缓存
        self._link_cache: Dict[int, Dict[Tuple[str, str], LinkState]] = {}
        self.cache_duration = config.get('communication', {}).get('cache_duration_steps', 5)
        
        logger.info("链路状态管理器初始化完成")
    
    @handle_errors(module="communication", function="compute_link_states")
    def compute_link_states(self, satellites: Dict, orbital_updater, time_step: int) -> Dict[Tuple[str, str], LinkState]:
        """
        基于orbital_updater的矩阵数据计算所有链路状态
        """
        # 检查缓存
        if time_step in self._link_cache:
            return self._link_cache[time_step]
        
        link_states = {}
        
        # 获取orbital_updater计算的矩阵数据
        try:
            # 星间链路
            inter_sat_matrix = orbital_updater.build_visibility_matrix(list(satellites.values()))
            if inter_sat_matrix.size > 0:
                link_states.update(self._process_inter_satellite_links(
                    list(satellites.keys()), inter_sat_matrix, orbital_updater))
            
            # 卫星-地面链路
            sat_ground_matrix = orbital_updater.build_satellite_ground_visibility_matrix(list(satellites.values()))
            if sat_ground_matrix.size > 0:
                link_states.update(self._process_satellite_ground_links(
                    list(satellites.keys()), sat_ground_matrix, orbital_updater))
            
            # 卫星-云链路
            sat_cloud_matrix = orbital_updater.build_satellite_cloud_visibility_matrix(list(satellites.values()))
            if sat_cloud_matrix.size > 0:
                link_states.update(self._process_satellite_cloud_links(
                    list(satellites.keys()), sat_cloud_matrix, orbital_updater))
            
        except Exception as e:
            logger.error(f"计算链路状态失败: {e}")
            return {}
        
        # 更新缓存
        self._link_cache[time_step] = link_states
        self._cleanup_cache(time_step)
        
        logger.debug(f"计算完成: {len(link_states)} 条链路")
        return link_states
    
    def _process_inter_satellite_links(self, satellite_ids: List[str], 
                                     visibility_matrix: np.ndarray, 
                                     orbital_updater) -> Dict[Tuple[str, str], LinkState]:
        """处理星间链路"""
        links = {}
        n_sats = len(satellite_ids)
        
        for i in range(n_sats):
            for j in range(i+1, n_sats):
                if i < visibility_matrix.shape[0] and j < visibility_matrix.shape[1] and visibility_matrix[i, j]:
                    sat1_id = satellite_ids[i]
                    sat2_id = satellite_ids[j]
                    
                    # 从orbital_updater获取距离
                    distance = safe_execute(
                        orbital_updater.calculate_distance,
                        sat1_id, sat2_id,
                        default_return=1000.0
                    )
                    
                    # 创建双向链路
                    link1 = self.calculator.calculate_complete_link_state(
                        sat1_id, sat2_id, distance, "inter_satellite")
                    link2 = self.calculator.calculate_complete_link_state(
                        sat2_id, sat1_id, distance, "inter_satellite")
                    
                    links[(sat1_id, sat2_id)] = link1
                    links[(sat2_id, sat1_id)] = link2
        
        return links
    
    def _process_satellite_ground_links(self, satellite_ids: List[str],
                                      visibility_matrix: np.ndarray,
                                      orbital_updater) -> Dict[Tuple[str, str], LinkState]:
        """处理卫星-地面链路"""
        links = {}
        ground_stations = getattr(orbital_updater, 'ground_stations', {})
        
        for i, sat_id in enumerate(satellite_ids):
            for j, (ground_id, ground_station) in enumerate(ground_stations.items()):
                if (i < visibility_matrix.shape[0] and j < visibility_matrix.shape[1] and 
                    visibility_matrix[i, j]):
                    
                    # 计算距离
                    distance = safe_execute(
                        orbital_updater.calculate_distance,
                        sat_id, ground_id,
                        default_return=800.0
                    )
                    
                    # 双向链路
                    link1 = self.calculator.calculate_complete_link_state(
                        sat_id, ground_id, distance, "satellite_to_ground")
                    link2 = self.calculator.calculate_complete_link_state(
                        ground_id, sat_id, distance, "ground_to_satellite")
                    
                    links[(sat_id, ground_id)] = link1
                    links[(ground_id, sat_id)] = link2
        
        return links
    
    def _process_satellite_cloud_links(self, satellite_ids: List[str],
                                     visibility_matrix: np.ndarray,
                                     orbital_updater) -> Dict[Tuple[str, str], LinkState]:
        """处理卫星-云链路"""
        links = {}
        cloud_stations = getattr(orbital_updater, 'cloud_stations', {})
        
        for i, sat_id in enumerate(satellite_ids):
            for j, (cloud_id, cloud_station) in enumerate(cloud_stations.items()):
                if (i < visibility_matrix.shape[0] and j < visibility_matrix.shape[1] and 
                    visibility_matrix[i, j]):
                    
                    # 计算距离
                    distance = safe_execute(
                        orbital_updater.calculate_distance,
                        sat_id, cloud_id,
                        default_return=1200.0
                    )
                    
                    # 双向链路
                    link1 = self.calculator.calculate_complete_link_state(
                        sat_id, cloud_id, distance, "satellite_to_cloud")
                    link2 = self.calculator.calculate_complete_link_state(
                        cloud_id, sat_id, distance, "cloud_to_satellite")
                    
                    links[(sat_id, cloud_id)] = link1
                    links[(cloud_id, sat_id)] = link2
        
        return links
    
    def _cleanup_cache(self, current_step: int):
        """清理过期缓存"""
        expired_steps = [step for step in self._link_cache.keys() 
                        if current_step - step > self.cache_duration]
        for step in expired_steps:
            self._link_cache.pop(step, None)


class CommunicationManagerRefactored:
    """
    重构后的通信管理器主类
    提供统一的对外接口，保持与原版本的兼容性
    """
    
    def __init__(self, config: Dict, time_manager: TimeManager):
        self.config = config
        self.time_manager = time_manager
        self.orbital_updater = None
        self.link_state_manager = LinkStateManager(config, time_manager)
        
        logger.info("重构后的通信管理器初始化完成")
    
    def set_orbital_updater(self, orbital_updater):
        """设置轨道更新器依赖"""
        self.orbital_updater = orbital_updater
        logger.debug("轨道更新器依赖已设置")
    
    @handle_errors(module="communication", function="get_all_link_states")  
    def get_all_link_states(self, time_step: int) -> Dict[Tuple[str, str], Dict[str, float]]:
        """
        获取所有链路状态 - 主要接口方法
        返回格式保持与原版本兼容
        """
        if self.orbital_updater is None:
            raise_error(ConfigurationError, "轨道更新器未设置", "ORBITAL_UPDATER_NOT_SET")
        
        # 获取卫星数据
        satellites = safe_execute(
            self.orbital_updater.get_satellites_at_time,
            time_step,
            default_return={}
        )
        
        if not satellites:
            logger.warning(f"时间步 {time_step} 未找到卫星数据")
            return {}
        
        # 计算链路状态
        link_states = self.link_state_manager.compute_link_states(
            satellites, self.orbital_updater, time_step
        )
        
        # 转换为兼容格式
        result = {}
        for (source_id, target_id), link_state in link_states.items():
            result[(source_id, target_id)] = {
                'distance_km': link_state.distance_km,
                'data_rate_mbps': link_state.data_rate_mbps,
                'transmission_delay_ms': link_state.transmission_delay_ms,
                'transmission_energy_j': link_state.transmission_energy_j,
                'signal_strength_dbm': link_state.signal_strength_dbm,
                'snr_db': link_state.snr_db,
                'link_type': link_state.link_type
            }
        
        logger.info(f"时隙 {time_step}: 计算完成 {len(result)} 条链路")
        return result
    
    def get_neighbors(self, node_id: str, time_step: int) -> List[str]:
        """获取节点的所有邻居"""
        neighbors = []
        link_states = self.get_all_link_states(time_step)
        
        for (source_id, target_id) in link_states.keys():
            if source_id == node_id:
                neighbors.append(target_id)
        
        return neighbors
    
    def get_link_state(self, source_id: str, target_id: str, time_step: int) -> Optional[Dict[str, float]]:
        """获取特定链路的状态"""
        all_states = self.get_all_link_states(time_step)
        return all_states.get((source_id, target_id))


if __name__ == "__main__":
    # 简单测试
    test_config = {
        'communication': {
            'frequency_ghz': 28.0,
            'transmit_power_w': 1.0,
            'bandwidth_mhz': 100.0,
            'antenna_gain_db': 20.0,
            'noise_temperature_k': 300.0
        }
    }
    
    calculator = LinkCalculator(test_config)
    
    # 测试单链路计算
    test_distance = 1000.0  # 1000km
    link_state = calculator.calculate_complete_link_state(
        "Satellite111", "Satellite112", test_distance, "inter_satellite"
    )
    
    print(f"测试链路状态:")
    print(f"  距离: {link_state.distance_km} km")
    print(f"  数据速率: {link_state.data_rate_mbps:.2f} Mbps")
    print(f"  传输延迟: {link_state.transmission_delay_ms:.2f} ms")
    print(f"  信噪比: {link_state.snr_db:.2f} dB")
    print(f"  信号强度: {link_state.signal_strength_dbm:.2f} dBm")
    print(f"  传输能耗: {link_state.transmission_energy_j:.2f} J")
    
    print("Communication模块基础测试完成")