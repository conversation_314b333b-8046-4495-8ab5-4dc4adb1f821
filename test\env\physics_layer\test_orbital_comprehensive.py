#!/usr/bin/env python3
"""
SPACE2卫星边缘计算仿真环境 - Orbital模块全面测试

基于CLAUDE.md和技术文档要求编写的全面系统测试代码
测试指定时隙（1-5, 100-105, 1000-1005）的所有核心模块功能

测试要求：
- 测试72颗卫星的位置信息 (纬度、经度、光照状态)
- 卫星间可见性矩阵 (72x72) - 显示连通性统计
- 卫星-地面可见性矩阵 (72x420) - 显示覆盖统计  
- 卫星-云可见性矩阵 (72x5) - 显示云连接统计
- 距离矩阵关键统计 (最小/最大/平均距离)
- 可见性变化分析 (与上一时隙对比)

作者: SPACE2开发组
日期: 2025-01-21
版本: v2.0 (72颗卫星配置)
"""

import sys
import os
import pytest
import numpy as np
import pandas as pd
from datetime import datetime
from typing import Dict, List, Tuple, Any
import logging

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', 'src'))

from env.physics_layer.orbital import OrbitalUpdater, Satellite, GroundStation

# Configure logging for test output
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class TestOrbitalComprehensive:
    """Orbital模块全面测试类"""
    
    @pytest.fixture(scope="class")
    def orbital_updater(self):
        """创建OrbitalUpdater测试夹具"""
        try:
            updater = OrbitalUpdater()
            logger.info(f"OrbitalUpdater初始化成功")
            logger.info(f"卫星数据记录: {len(updater.satellite_data)}")
            logger.info(f"地面站数量: {updater.get_ground_station_count()}")  
            logger.info(f"云中心数量: {updater.get_cloud_station_count()}")
            logger.info(f"总时隙数: {updater.get_total_timeslots()}")
            return updater
        except Exception as e:
            pytest.fail(f"OrbitalUpdater初始化失败: {e}")
    
    @pytest.fixture(scope="class")
    def test_timeslots(self):
        """定义测试时隙"""
        return {
            'early': [1, 2, 3, 4, 5],           # 系统启动阶段
            'middle': [100, 101, 102, 103, 104, 105],  # 系统稳定运行阶段
            'late': [1000, 1001, 1002, 1003, 1004, 1005]  # 系统长期运行阶段
        }
    
    def test_system_configuration(self, orbital_updater):
        """测试系统配置验证"""
        print("\n" + "="*80)
        print("系统配置验证测试")
        print("="*80)
        
        # 验证72颗卫星配置
        test_satellites = orbital_updater.get_satellites_at_time(1)
        assert len(test_satellites) == 72, f"期望72颗卫星，实际获得{len(test_satellites)}颗"
        
        # 验证420个地面站配置
        assert orbital_updater.get_ground_station_count() == 420, \
            f"期望420个地面站，实际获得{orbital_updater.get_ground_station_count()}个"
        
        # 验证5个云中心配置
        assert orbital_updater.get_cloud_station_count() == 5, \
            f"期望5个云中心，实际获得{orbital_updater.get_cloud_station_count()}个"
        
        print("系统配置验证通过")
        print(f"  - 卫星数量: {len(test_satellites)}")
        print(f"  - 地面站数量: {orbital_updater.get_ground_station_count()}")
        print(f"  - 云中心数量: {orbital_updater.get_cloud_station_count()}")
    
    def test_satellite_positions_detailed(self, orbital_updater, test_timeslots):
        """测试72颗卫星位置信息详细输出"""
        print("\n" + "="*80)
        print("72颗卫星位置信息详细测试")
        print("="*80)
        
        all_timeslots = []
        for phase_name, timeslots in test_timeslots.items():
            all_timeslots.extend(timeslots)
        
        for timeslot in all_timeslots:
            print(f"\n{'='*20} 时隙 {timeslot} {'='*20}")
            
            satellites = orbital_updater.get_satellites_at_time(timeslot)
            assert len(satellites) == 72, f"时隙{timeslot}: 期望72颗卫星，获得{len(satellites)}颗"
            
            # 统计光照状态
            illuminated_count = sum(1 for sat in satellites.values() if sat.illuminated)
            dark_count = 72 - illuminated_count
            
            print(f"时隙 {timeslot} 卫星状态统计:")
            print(f"  总卫星数: {len(satellites)}")
            print(f"  光照卫星: {illuminated_count} ({illuminated_count/72*100:.1f}%)")
            print(f"  阴影卫星: {dark_count} ({dark_count/72*100:.1f}%)")
            
            # 位置分布统计
            latitudes = [sat.latitude for sat in satellites.values()]
            longitudes = [sat.longitude for sat in satellites.values()]
            
            print(f"  纬度范围: [{min(latitudes):.2f}, {max(latitudes):.2f}]")
            print(f"  经度范围: [{min(longitudes):.2f}, {max(longitudes):.2f}]")
            
            # 输出前10颗卫星详细位置信息
            print(f"  前10颗卫星详细位置:")
            for i, (sat_id, sat) in enumerate(list(satellites.items())[:10]):
                print(f"    {sat_id}: lat={sat.latitude:7.3f}°, lon={sat.longitude:8.3f}°, "
                      f"light={'Y' if sat.illuminated else 'N'}")
            
            # 验证坐标范围合理性
            assert all(-90 <= lat <= 90 for lat in latitudes), "纬度超出有效范围"
            assert all(-180 <= lon <= 180 for lon in longitudes), "经度超出有效范围"
    
    def test_inter_satellite_visibility_matrices(self, orbital_updater, test_timeslots):
        """测试卫星间可见性矩阵(72x72)"""
        print("\n" + "="*80) 
        print("卫星间可见性矩阵(72x72)详细测试")
        print("="*80)
        
        previous_vis_matrix = None
        
        for phase_name, timeslots in test_timeslots.items():
            print(f"\n{'-'*20} {phase_name.upper()} 阶段 {'-'*20}")
            
            for timeslot in timeslots:
                satellites = orbital_updater.get_satellites_at_time(timeslot)
                vis_matrix, dist_matrix = orbital_updater.build_visibility_matrix(satellites)
                
                # 验证矩阵维度
                assert vis_matrix.shape == (72, 72), f"时隙{timeslot}: 可见性矩阵维度错误"
                assert dist_matrix.shape == (72, 72), f"时隙{timeslot}: 距离矩阵维度错误"
                
                # 验证矩阵属性
                assert np.allclose(vis_matrix, vis_matrix.T), f"时隙{timeslot}: 可见性矩阵非对称"
                assert np.all(np.diag(vis_matrix) == False), f"时隙{timeslot}: 对角线应为False"
                assert np.all(dist_matrix >= 0), f"时隙{timeslot}: 距离矩阵存在负值"
                
                # 统计连通性
                visible_pairs = np.sum(vis_matrix) // 2  # 除以2因为对称矩阵
                total_pairs = 72 * 71 // 2
                connectivity_ratio = visible_pairs / total_pairs * 100
                
                print(f"时隙 {timeslot} 卫星间连通性:")
                print(f"  可见卫星对: {visible_pairs}/{total_pairs} ({connectivity_ratio:.1f}%)")
                
                if visible_pairs > 0:
                    visible_distances = dist_matrix[vis_matrix]
                    print(f"  可见距离统计:")
                    print(f"    最小距离: {visible_distances.min():.2f} km")
                    print(f"    最大距离: {visible_distances.max():.2f} km")
                    print(f"    平均距离: {visible_distances.mean():.2f} km")
                    print(f"    标准差: {visible_distances.std():.2f} km")
                
                # 可见性变化分析
                if previous_vis_matrix is not None:
                    changes = np.sum(vis_matrix != previous_vis_matrix)
                    new_links = np.sum((vis_matrix & ~previous_vis_matrix))
                    lost_links = np.sum((~vis_matrix & previous_vis_matrix))
                    print(f"  与上一时隙变化:")
                    print(f"    总变化: {changes}")
                    print(f"    新增链路: {new_links}")
                    print(f"    失去链路: {lost_links}")
                
                previous_vis_matrix = vis_matrix.copy()
                
                # 输出部分矩阵用于验证
                print(f"  可见性矩阵(前8x8):")
                print(vis_matrix[:8, :8].astype(int))
    
    def test_satellite_ground_visibility_matrices(self, orbital_updater, test_timeslots):
        """测试卫星-地面可见性矩阵(72x420)"""
        print("\n" + "="*80)
        print("卫星-地面可见性矩阵(72x420)详细测试")
        print("="*80)
        
        previous_vis_matrix = None
        
        for phase_name, timeslots in test_timeslots.items():
            print(f"\n{'-'*20} {phase_name.upper()} 阶段 {'-'*20}")
            
            for timeslot in timeslots:
                satellites = orbital_updater.get_satellites_at_time(timeslot)
                vis_matrix, dist_matrix = orbital_updater.build_satellite_ground_visibility_matrix(satellites, timeslot)
                
                # 验证矩阵维度
                assert vis_matrix.shape == (72, 420), f"时隙{timeslot}: 地面可见性矩阵维度错误"
                assert dist_matrix.shape == (72, 420), f"时隙{timeslot}: 地面距离矩阵维度错误"
                
                # 验证距离矩阵
                assert np.all(dist_matrix >= 0), f"时隙{timeslot}: 地面距离矩阵存在负值"
                
                # 统计覆盖情况
                total_links = np.sum(vis_matrix)
                covered_ground_stations = np.sum(np.any(vis_matrix, axis=0))
                active_satellites = np.sum(np.any(vis_matrix, axis=1))
                
                print(f"时隙 {timeslot} 卫星-地面覆盖:")
                print(f"  总连接数: {total_links}")
                print(f"  覆盖地面站: {covered_ground_stations}/420 ({covered_ground_stations/420*100:.1f}%)")
                print(f"  活跃卫星: {active_satellites}/72 ({active_satellites/72*100:.1f}%)")
                
                # 每颗卫星平均覆盖的地面站数
                links_per_satellite = np.sum(vis_matrix, axis=1)
                print(f"  每卫星平均覆盖: {links_per_satellite.mean():.1f} 地面站")
                print(f"  最大覆盖: {links_per_satellite.max()} 地面站")
                print(f"  最小覆盖: {links_per_satellite.min()} 地面站")
                
                # 距离统计
                if total_links > 0:
                    link_distances = dist_matrix[vis_matrix]
                    print(f"  连接距离统计:")
                    print(f"    最小距离: {link_distances.min():.2f} km")
                    print(f"    最大距离: {link_distances.max():.2f} km")
                    print(f"    平均距离: {link_distances.mean():.2f} km")
                
                # 可见性变化分析
                if previous_vis_matrix is not None:
                    changes = np.sum(vis_matrix != previous_vis_matrix)
                    new_links = np.sum((vis_matrix & ~previous_vis_matrix))
                    lost_links = np.sum((~vis_matrix & previous_vis_matrix))
                    print(f"  与上一时隙变化:")
                    print(f"    总变化: {changes}")
                    print(f"    新增链路: {new_links}")  
                    print(f"    失去链路: {lost_links}")
                
                previous_vis_matrix = vis_matrix.copy()
                
                # 输出部分矩阵用于验证
                print(f"  可见性矩阵(前5卫星x前10地面站):")
                print(vis_matrix[:5, :10].astype(int))
    
    def test_satellite_cloud_visibility_matrices(self, orbital_updater, test_timeslots):
        """测试卫星-云可见性矩阵(72x5)"""
        print("\n" + "="*80)
        print("卫星-云可见性矩阵(72x5)详细测试") 
        print("="*80)
        
        previous_vis_matrix = None
        
        for phase_name, timeslots in test_timeslots.items():
            print(f"\n{'-'*20} {phase_name.upper()} 阶段 {'-'*20}")
            
            for timeslot in timeslots:
                satellites = orbital_updater.get_satellites_at_time(timeslot)
                vis_matrix, dist_matrix = orbital_updater.build_satellite_cloud_visibility_matrix(satellites, timeslot)
                
                # 验证矩阵维度
                assert vis_matrix.shape == (72, 5), f"时隙{timeslot}: 云可见性矩阵维度错误"
                assert dist_matrix.shape == (72, 5), f"时隙{timeslot}: 云距离矩阵维度错误"
                
                # 验证距离矩阵
                assert np.all(dist_matrix >= 0), f"时隙{timeslot}: 云距离矩阵存在负值"
                
                # 统计云连接情况
                total_links = np.sum(vis_matrix)
                connected_clouds = np.sum(np.any(vis_matrix, axis=0))
                connected_satellites = np.sum(np.any(vis_matrix, axis=1))
                
                print(f"时隙 {timeslot} 卫星-云连接:")
                print(f"  总连接数: {total_links}")
                print(f"  连接云中心: {connected_clouds}/5")
                print(f"  连接卫星: {connected_satellites}/72 ({connected_satellites/72*100:.1f}%)")
                
                # 每个云中心的连接统计
                cloud_connections = np.sum(vis_matrix, axis=0)
                for i, conn_count in enumerate(cloud_connections):
                    print(f"  云中心{i+1}: {conn_count} 卫星连接")
                
                # 距离统计
                if total_links > 0:
                    link_distances = dist_matrix[vis_matrix]
                    print(f"  连接距离统计:")
                    print(f"    最小距离: {link_distances.min():.2f} km")
                    print(f"    最大距离: {link_distances.max():.2f} km")
                    print(f"    平均距离: {link_distances.mean():.2f} km")
                
                # 可见性变化分析
                if previous_vis_matrix is not None:
                    changes = np.sum(vis_matrix != previous_vis_matrix)
                    new_links = np.sum((vis_matrix & ~previous_vis_matrix))
                    lost_links = np.sum((~vis_matrix & previous_vis_matrix))
                    print(f"  与上一时隙变化:")
                    print(f"    总变化: {changes}")
                    print(f"    新增链路: {new_links}")
                    print(f"    失去链路: {lost_links}")
                
                previous_vis_matrix = vis_matrix.copy()
                
                # 输出完整矩阵（只有72x5，可以完整显示）
                print(f"  完整可见性矩阵(72x5):")
                print(vis_matrix.astype(int))
                print(f"  完整距离矩阵(72x5, km):")
                print(np.round(dist_matrix, 1))
    
    def test_distance_matrices_statistics(self, orbital_updater, test_timeslots):
        """测试距离矩阵关键统计"""
        print("\n" + "="*80)
        print("距离矩阵关键统计分析")
        print("="*80)
        
        statistics_summary = []
        
        for phase_name, timeslots in test_timeslots.items():
            print(f"\n{'-'*20} {phase_name.upper()} 阶段统计 {'-'*20}")
            
            for timeslot in timeslots:
                satellites = orbital_updater.get_satellites_at_time(timeslot)
                
                # 获取三种距离矩阵
                sat_vis, sat_dist = orbital_updater.build_visibility_matrix(satellites)
                ground_vis, ground_dist = orbital_updater.build_satellite_ground_visibility_matrix(satellites, timeslot)
                cloud_vis, cloud_dist = orbital_updater.build_satellite_cloud_visibility_matrix(satellites, timeslot)
                
                stats = {
                    'timeslot': timeslot,
                    'phase': phase_name,
                    'inter_satellite': self._analyze_distance_matrix(sat_dist, sat_vis, "卫星间"),
                    'satellite_ground': self._analyze_distance_matrix(ground_dist, ground_vis, "卫星-地面"), 
                    'satellite_cloud': self._analyze_distance_matrix(cloud_dist, cloud_vis, "卫星-云")
                }
                
                statistics_summary.append(stats)
                
                print(f"\n时隙 {timeslot} 距离统计:")
                for matrix_type, stat in stats.items():
                    if matrix_type in ['timeslot', 'phase']:
                        continue
                    print(f"  {stat['name']}:")
                    print(f"    最小距离: {stat['min_dist']:.2f} km")
                    print(f"    最大距离: {stat['max_dist']:.2f} km")
                    print(f"    平均距离: {stat['mean_dist']:.2f} km")
                    print(f"    标准差: {stat['std_dist']:.2f} km")
                    print(f"    连接数: {stat['connection_count']}")
        
        # 生成阶段性统计报告
        self._generate_phase_statistics_report(statistics_summary)
    
    def _analyze_distance_matrix(self, dist_matrix: np.ndarray, vis_matrix: np.ndarray, 
                               matrix_name: str) -> Dict[str, Any]:
        """分析距离矩阵统计信息"""
        if np.any(vis_matrix):
            visible_distances = dist_matrix[vis_matrix]
            return {
                'name': matrix_name,
                'min_dist': float(visible_distances.min()),
                'max_dist': float(visible_distances.max()),
                'mean_dist': float(visible_distances.mean()),
                'std_dist': float(visible_distances.std()),
                'connection_count': int(np.sum(vis_matrix))
            }
        else:
            return {
                'name': matrix_name,
                'min_dist': 0.0,
                'max_dist': 0.0,
                'mean_dist': 0.0,
                'std_dist': 0.0,
                'connection_count': 0
            }
    
    def _generate_phase_statistics_report(self, statistics_summary: List[Dict]):
        """生成阶段性统计报告"""
        print("\n" + "="*60)
        print("阶段性统计报告")
        print("="*60)
        
        phases = ['early', 'middle', 'late']
        phase_names = {'early': '启动阶段', 'middle': '稳定阶段', 'late': '长期运行阶段'}
        
        for phase in phases:
            phase_stats = [s for s in statistics_summary if s['phase'] == phase]
            if not phase_stats:
                continue
                
            print(f"\n{phase_names[phase]} ({phase.upper()}):")
            
            # 计算平均值
            inter_sat_means = [s['inter_satellite']['mean_dist'] for s in phase_stats if s['inter_satellite']['connection_count'] > 0]
            ground_means = [s['satellite_ground']['mean_dist'] for s in phase_stats if s['satellite_ground']['connection_count'] > 0]
            cloud_means = [s['satellite_cloud']['mean_dist'] for s in phase_stats if s['satellite_cloud']['connection_count'] > 0]
            
            if inter_sat_means:
                print(f"  卫星间平均距离: {np.mean(inter_sat_means):.2f} km")
            if ground_means:
                print(f"  卫星-地面平均距离: {np.mean(ground_means):.2f} km")  
            if cloud_means:
                print(f"  卫星-云平均距离: {np.mean(cloud_means):.2f} km")
            
            # 计算连接数平均值
            inter_sat_connections = [s['inter_satellite']['connection_count'] for s in phase_stats]
            ground_connections = [s['satellite_ground']['connection_count'] for s in phase_stats]
            cloud_connections = [s['satellite_cloud']['connection_count'] for s in phase_stats]
            
            print(f"  平均卫星间连接: {np.mean(inter_sat_connections):.1f}")
            print(f"  平均地面连接: {np.mean(ground_connections):.1f}")
            print(f"  平均云连接: {np.mean(cloud_connections):.1f}")
    
    def test_visibility_change_analysis(self, orbital_updater, test_timeslots):
        """测试可见性变化分析"""
        print("\n" + "="*80)
        print("可见性变化分析详细测试")
        print("="*80)
        
        all_timeslots = []
        for phase_name, timeslots in test_timeslots.items():
            all_timeslots.extend(timeslots)
        
        all_timeslots.sort()
        
        previous_matrices = None
        
        for i, timeslot in enumerate(all_timeslots):
            if i == 0:
                # 第一个时隙，建立基线
                satellites = orbital_updater.get_satellites_at_time(timeslot)
                sat_vis, _ = orbital_updater.build_visibility_matrix(satellites)
                ground_vis, _ = orbital_updater.build_satellite_ground_visibility_matrix(satellites, timeslot)
                cloud_vis, _ = orbital_updater.build_satellite_cloud_visibility_matrix(satellites, timeslot)
                
                previous_matrices = {
                    'satellite': sat_vis,
                    'ground': ground_vis, 
                    'cloud': cloud_vis
                }
                
                print(f"时隙 {timeslot} (基线):")
                print(f"  卫星间连接: {np.sum(sat_vis) // 2}")
                print(f"  地面连接: {np.sum(ground_vis)}")
                print(f"  云连接: {np.sum(cloud_vis)}")
                continue
            
            # 分析变化
            satellites = orbital_updater.get_satellites_at_time(timeslot)
            sat_vis, _ = orbital_updater.build_visibility_matrix(satellites)
            ground_vis, _ = orbital_updater.build_satellite_ground_visibility_matrix(satellites, timeslot)
            cloud_vis, _ = orbital_updater.build_satellite_cloud_visibility_matrix(satellites, timeslot)
            
            current_matrices = {
                'satellite': sat_vis,
                'ground': ground_vis,
                'cloud': cloud_vis
            }
            
            print(f"\n时隙 {timeslot} vs {all_timeslots[i-1]}:")
            
            # 分析各类型矩阵的变化
            for matrix_type in ['satellite', 'ground', 'cloud']:
                prev = previous_matrices[matrix_type]
                curr = current_matrices[matrix_type]
                
                # 计算变化
                changes = np.sum(prev != curr)
                new_links = np.sum(curr & ~prev)
                lost_links = np.sum(~curr & prev)
                stable_links = np.sum(curr & prev)
                
                change_rate = changes / prev.size * 100
                
                matrix_names = {
                    'satellite': '卫星间',
                    'ground': '地面',
                    'cloud': '云'
                }
                
                print(f"  {matrix_names[matrix_type]}连接变化:")
                print(f"    总变化: {changes} ({change_rate:.2f}%)")
                print(f"    新增: {new_links}")
                print(f"    失去: {lost_links}")
                print(f"    稳定: {stable_links}")
                print(f"    当前总数: {np.sum(curr)}")
            
            previous_matrices = current_matrices
    
    def test_comprehensive_system_validation(self, orbital_updater, test_timeslots):
        """综合系统验证测试"""
        print("\n" + "="*80)
        print("综合系统验证")
        print("="*80)
        
        validation_results = {
            'satellite_count_consistency': True,
            'matrix_dimension_consistency': True,
            'distance_threshold_compliance': True,
            'temporal_continuity': True,
            'data_integrity': True
        }
        
        all_timeslots = []
        for phase_name, timeslots in test_timeslots.items():
            all_timeslots.extend(timeslots)
        
        satellite_counts = []
        
        for timeslot in all_timeslots:
            satellites = orbital_updater.get_satellites_at_time(timeslot)
            satellite_counts.append(len(satellites))
            
            # 验证卫星数量一致性
            if len(satellites) != 72:
                validation_results['satellite_count_consistency'] = False
                logger.error(f"时隙{timeslot}: 卫星数量不一致: {len(satellites)}")
            
            # 验证矩阵维度
            sat_vis, sat_dist = orbital_updater.build_visibility_matrix(satellites)
            ground_vis, ground_dist = orbital_updater.build_satellite_ground_visibility_matrix(satellites, timeslot)
            cloud_vis, cloud_dist = orbital_updater.build_satellite_cloud_visibility_matrix(satellites, timeslot)
            
            expected_shapes = [(72, 72), (72, 420), (72, 5)]
            actual_shapes = [sat_vis.shape, ground_vis.shape, cloud_vis.shape]
            
            if actual_shapes != expected_shapes:
                validation_results['matrix_dimension_consistency'] = False
                logger.error(f"时隙{timeslot}: 矩阵维度不符合预期")
            
            # 验证距离阈值合规性
            visible_inter_sat = sat_dist[sat_vis]
            visible_ground = ground_dist[ground_vis] 
            visible_cloud = cloud_dist[cloud_vis]
            
            if len(visible_inter_sat) > 0 and np.max(visible_inter_sat) > orbital_updater.visibility_threshold:
                validation_results['distance_threshold_compliance'] = False
                logger.error(f"时隙{timeslot}: 卫星间距离超过阈值")
            
            if len(visible_ground) > 0 and np.max(visible_ground) > orbital_updater.ground_visibility_threshold:
                validation_results['distance_threshold_compliance'] = False
                logger.error(f"时隙{timeslot}: 地面距离超过阈值")
            
            if len(visible_cloud) > 0 and np.max(visible_cloud) > orbital_updater.cloud_visibility_threshold:
                validation_results['distance_threshold_compliance'] = False
                logger.error(f"时隙{timeslot}: 云距离超过阈值")
        
        # 生成验证报告
        print("验证结果:")
        for check, result in validation_results.items():
            status = "✓ 通过" if result else "✗ 失败"
            print(f"  {check}: {status}")
        
        print(f"\n系统统计:")
        print(f"  测试时隙数: {len(all_timeslots)}")
        print(f"  卫星数量范围: {min(satellite_counts)}-{max(satellite_counts)}")
        print(f"  卫星数量一致性: {'✓' if len(set(satellite_counts)) == 1 else '✗'}")
        
        # 生成测试报告文件
        self._generate_test_report(orbital_updater, all_timeslots, validation_results)
    
    def _generate_test_report(self, orbital_updater, test_timeslots, validation_results):
        """生成测试报告文件"""
        report_file = "orbital_test_report.txt"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("SPACE2 Orbital模块测试报告\n")
            f.write("="*50 + "\n")
            f.write(f"测试日期: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"测试版本: v2.0 (72颗卫星配置)\n")
            f.write(f"测试时隙: {test_timeslots}\n\n")
            
            f.write("系统配置:\n")
            f.write(f"  卫星数量: 72\n")
            f.write(f"  地面站数量: {orbital_updater.get_ground_station_count()}\n") 
            f.write(f"  云中心数量: {orbital_updater.get_cloud_station_count()}\n")
            f.write(f"  总时隙数: {orbital_updater.get_total_timeslots()}\n\n")
            
            f.write("验证结果:\n")
            for check, result in validation_results.items():
                status = "通过" if result else "失败"
                f.write(f"  {check}: {status}\n")
            
            f.write(f"\n所有验证项目: {'全部通过' if all(validation_results.values()) else '部分失败'}\n")
        
        print(f"测试报告已生成: {report_file}")


# 独立运行函数，用于pytest之外的直接执行
def run_comprehensive_tests():
    """运行所有comprehensive测试"""
    print("SPACE2 Orbital模块全面测试")
    print("="*80)
    
    test_class = TestOrbitalComprehensive()
    
    # 创建测试夹具
    orbital_updater = OrbitalUpdater()
    test_timeslots = {
        'early': [1, 2, 3, 4, 5],
        'middle': [100, 101, 102, 103, 104, 105], 
        'late': [1000, 1001, 1002, 1003, 1004, 1005]
    }
    
    try:
        # 运行所有测试
        test_class.test_system_configuration(orbital_updater)
        test_class.test_satellite_positions_detailed(orbital_updater, test_timeslots)
        test_class.test_inter_satellite_visibility_matrices(orbital_updater, test_timeslots)
        test_class.test_satellite_ground_visibility_matrices(orbital_updater, test_timeslots)
        test_class.test_satellite_cloud_visibility_matrices(orbital_updater, test_timeslots)
        test_class.test_distance_matrices_statistics(orbital_updater, test_timeslots)
        test_class.test_visibility_change_analysis(orbital_updater, test_timeslots)
        test_class.test_comprehensive_system_validation(orbital_updater, test_timeslots)
        
        print("\n" + "="*80)
        print("全面测试完成！所有测试用例执行成功")
        print("="*80)
        
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 直接运行comprehensive测试
    run_comprehensive_tests()