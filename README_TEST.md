# SPACE2 Orbital模块测试文档

## 概述

本文档描述了SPACE2卫星边缘计算仿真环境orbital.py模块的全面测试系统。测试基于CLAUDE.md规范，专门针对72颗LEO卫星、420个地面用户终端和5个云计算中心的系统配置进行验证。

## 测试目标

### 核心测试范围
- **测试时隙**: 1-5 (启动阶段), 100-105 (稳定阶段), 1000-1005 (长期运行阶段)
- **卫星数量**: 72颗LEO卫星
- **地面站数量**: 420个全球分布的用户终端
- **云中心数量**: 5个战略位置的云计算中心

### 测试内容
1. **卫星位置信息验证** - 72颗卫星的纬度、经度、光照状态
2. **卫星间可见性矩阵** - 72×72矩阵，显示连通性统计
3. **卫星-地面可见性矩阵** - 72×420矩阵，显示覆盖统计
4. **卫星-云可见性矩阵** - 72×5矩阵，显示云连接统计
5. **距离矩阵统计** - 最小/最大/平均距离分析
6. **可见性变化分析** - 与上一时隙的对比分析

## 文件结构

```
SPACE2/
├── test/
│   ├── conftest.py                                    # pytest全局配置
│   └── env/
│       └── physics_layer/
│           ├── test_orbital.py                        # 原有简单测试
│           └── test_orbital_comprehensive.py          # 新建全面测试
├── pytest.ini                                        # pytest配置文件
├── run_orbital_tests.py                              # 测试运行器
└── README_TEST.md                                     # 本文档
```

## 运行测试

### 方法1: 直接运行comprehensive测试
```bash
# 完整的comprehensive测试
python run_orbital_tests.py

# 快速测试（减少时隙数量）
python run_orbital_tests.py --quick
```

### 方法2: 使用pytest运行
```bash
# 使用pytest运行comprehensive测试
python run_orbital_tests.py --pytest

# 直接使用pytest
pytest test/env/physics_layer/test_orbital_comprehensive.py -v

# 运行特定测试方法
pytest test/env/physics_layer/test_orbital_comprehensive.py::TestOrbitalComprehensive::test_satellite_positions_detailed -v
```

### 方法3: 直接运行测试文件
```bash
# 直接执行comprehensive测试
python test/env/physics_layer/test_orbital_comprehensive.py
```

## 测试输出

### 控制台输出示例
```
================================================================================
72颗卫星位置信息详细测试
================================================================================

==================== 时隙 1 ====================
时隙 1 卫星状态统计:
  总卫星数: 72
  光照卫星: 36 (50.0%)
  阴影卫星: 36 (50.0%)
  纬度范围: [-89.50, 89.50]
  经度范围: [-179.90, 179.90]
  前10颗卫星详细位置:
    111: lat= 23.456°, lon= -45.123°, light=✓
    112: lat=-12.345°, lon=  67.890°, light=✗
    ...

==================== 时隙 1 ====================
时隙 1 卫星间连通性:
  可见卫星对: 156/2556 (6.1%)
  可见距离统计:
    最小距离: 245.67 km
    最大距离: 1998.23 km
    平均距离: 1156.45 km
    标准差: 456.78 km
```

### 生成文件
- `orbital_test_report.txt` - 详细测试报告
- `timeslot_*.npz` - 每个时隙的矩阵数据（用于进一步分析）

## 测试类和方法

### TestOrbitalComprehensive类

#### 核心测试方法
- `test_system_configuration()` - 系统配置验证
- `test_satellite_positions_detailed()` - 卫星位置详细测试
- `test_inter_satellite_visibility_matrices()` - 卫星间可见性矩阵测试
- `test_satellite_ground_visibility_matrices()` - 卫星-地面可见性矩阵测试
- `test_satellite_cloud_visibility_matrices()` - 卫星-云可见性矩阵测试
- `test_distance_matrices_statistics()` - 距离矩阵统计分析
- `test_visibility_change_analysis()` - 可见性变化分析
- `test_comprehensive_system_validation()` - 综合系统验证

#### 辅助方法
- `_analyze_distance_matrix()` - 分析距离矩阵统计
- `_generate_phase_statistics_report()` - 生成阶段统计报告
- `_generate_test_report()` - 生成测试报告文件

## 验证标准

### 系统配置验证
- ✅ 72颗卫星配置正确
- ✅ 420个地面站配置正确  
- ✅ 5个云中心配置正确
- ✅ 1441个时隙配置正确

### 矩阵验证标准
- ✅ 卫星间矩阵维度: 72×72
- ✅ 地面矩阵维度: 72×420
- ✅ 云矩阵维度: 72×5
- ✅ 对称性验证（卫星间矩阵）
- ✅ 距离阈值合规性验证

### 数据完整性验证
- ✅ 无负距离值
- ✅ 坐标范围合理性
- ✅ 时间序列连续性
- ✅ 矩阵一致性

## 性能要求

### 执行时间预期
- 完整测试: ~2-5分钟（取决于系统性能）
- 快速测试: ~30-60秒
- 单个时隙测试: ~5-10秒

### 内存使用
- 矩阵内存占用: ~50MB（所有三种矩阵）
- 缓存内存占用: ~20-30MB
- 总内存需求: ~100-200MB

## 故障排除

### 常见问题

1. **模块导入错误**
   ```
   ModuleNotFoundError: No module named 'env.physics_layer.orbital'
   ```
   解决方案: 确保在SPACE2项目根目录运行测试

2. **数据文件缺失**
   ```
   FileNotFoundError: satellite_data72_1.csv
   ```
   解决方案: 确保数据文件存在于`src/env/env_data/`目录

3. **配置文件错误**
   ```
   Failed to load config file: config.yaml
   ```
   解决方案: 检查`src/env/physics_layer/config.yaml`是否存在

### 调试建议
- 使用`--quick`模式进行快速验证
- 检查生成的`orbital_test_report.txt`文件
- 启用详细日志输出: `export PYTHONPATH=.`

## 扩展测试

### 添加新的测试时隙
在`test_timeslots` fixture中添加新的时隙:
```python
@pytest.fixture(scope="class")
def test_timeslots(self):
    return {
        'early': [1, 2, 3, 4, 5],
        'middle': [100, 101, 102, 103, 104, 105],
        'late': [1000, 1001, 1002, 1003, 1004, 1005],
        'extended': [1200, 1300, 1400]  # 新增测试阶段
    }
```

### 自定义验证规则
可以在测试方法中添加自定义验证逻辑:
```python
def test_custom_validation(self, orbital_updater):
    # 自定义测试逻辑
    pass
```

## 报告和分析

测试运行后会生成详细的报告文件，包含:
- 系统配置摘要
- 每个时隙的详细统计
- 阶段性分析报告
- 验证结果汇总

建议定期运行测试以确保系统功能的正确性和稳定性。

---

**版本**: v2.0  
**更新日期**: 2025-01-21  
**维护**: SPACE2开发组