[tool:pytest]
# SPACE2 pytest配置文件
# 遵循CLAUDE.md测试规范

# 测试发现配置
testpaths = test
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# 输出配置
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --color=yes

# 标记定义
markers =
    orbital: 轨道模块测试
    integration: 集成测试  
    performance: 性能测试
    slow: 慢速测试

# 日志配置
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# 警告过滤
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning