#!/usr/bin/env python3
"""
SPACE2 Orbital模块测试运行器

快速运行orbital模块的comprehensive测试，输出详细的系统测试结果
基于CLAUDE.md要求，测试指定时隙(1-5, 100-105, 1000-1005)的所有核心功能

使用方法:
    python run_orbital_tests.py              # 运行完整测试
    python run_orbital_tests.py --quick      # 运行快速测试
    python run_orbital_tests.py --pytest    # 使用pytest运行
"""

import sys
import os
import argparse
from pathlib import Path

# 添加src路径
project_root = Path(__file__).parent
src_path = project_root / "src" 
sys.path.insert(0, str(src_path))

def run_comprehensive_tests():
    """运行comprehensive测试"""
    print("启动SPACE2 Orbital模块全面测试")
    print("="*80)
    
    try:
        # 添加test目录到路径
        test_path = project_root / "test"
        sys.path.insert(0, str(test_path))
        
        from env.physics_layer.test_orbital_comprehensive import run_comprehensive_tests
        run_comprehensive_tests()
        return True
    except Exception as e:
        print(f"❌ 测试运行失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_quick_tests():
    """运行快速测试（仅测试部分时隙）"""
    print("启动快速测试模式")
    print("="*50)
    
    try:
        # 添加test目录到路径
        test_path = project_root / "test"
        sys.path.insert(0, str(test_path))
        
        from env.physics_layer.test_orbital_comprehensive import TestOrbitalComprehensive
        from env.physics_layer.orbital import OrbitalUpdater
        
        test_class = TestOrbitalComprehensive()
        orbital_updater = OrbitalUpdater()
        test_timeslots = {
            'early': [1, 2],      # 减少测试时隙
            'middle': [100, 101], 
            'late': [1000, 1001]
        }
        
        # 运行核心测试
        test_class.test_system_configuration(orbital_updater)
        test_class.test_satellite_positions_detailed(orbital_updater, test_timeslots)
        test_class.test_inter_satellite_visibility_matrices(orbital_updater, test_timeslots)
        
        print("\n快速测试完成")
        return True
        
    except Exception as e:
        print(f"快速测试失败: {e}")
        return False

def run_with_pytest():
    """使用pytest运行测试"""
    print("使用pytest运行测试")
    print("="*50)
    
    import subprocess
    
    try:
        # 运行pytest
        result = subprocess.run([
            sys.executable, "-m", "pytest", 
            "test/env/physics_layer/test_orbital_comprehensive.py",
            "-v", "--tb=short"
        ], capture_output=True, text=True, cwd=project_root)
        
        print("PYTEST 输出:")
        print(result.stdout)
        
        if result.stderr:
            print("PYTEST 错误:")
            print(result.stderr)
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"pytest运行失败: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='SPACE2 Orbital模块测试运行器')
    parser.add_argument('--quick', action='store_true', help='运行快速测试')
    parser.add_argument('--pytest', action='store_true', help='使用pytest运行')
    
    args = parser.parse_args()
    
    print(f"SPACE2 Orbital模块测试运行器 v2.0")
    print(f"项目根目录: {project_root}")
    print(f"Python版本: {sys.version}")
    print("-"*80)
    
    success = False
    
    if args.pytest:
        success = run_with_pytest()
    elif args.quick:
        success = run_quick_tests()
    else:
        success = run_comprehensive_tests()
    
    print("\n" + "="*80)
    if success:
        print("测试运行成功完成！")
        print("所有测试用例均已执行")
        print("检查控制台输出和生成的报告文件")
    else:
        print("测试运行过程中出现问题")
        print("请检查错误信息并修复相关问题")
    
    print("="*80)
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)