AI编程助手测试文档撰写指南
在开始任何编程任务之前，你必须先撰写一份完整的说明文档。请按照以下结构进行：

1. 问题分析
核心问题识别：明确要解决的具体问题
问题复杂度评估：分析问题的技术难度和业务复杂度
约束条件分析：识别性能、资源、兼容性等限制因素
风险点识别：预判可能出现的技术风险和边界情况
2. 项目协作分析
现有程序依赖：分析需要调用或集成的已有模块
后续程序预留：识别后续模块可能需要的接口和数据
模块边界定义：明确当前模块的职责范围和边界
数据传递规范：定义与其他模块的数据交换格式和协议
3. 方法选择与论证
候选方案列举：列出2-3种可行的技术方案
方案对比分析：从性能、可维护性、扩展性、协作性等维度对比
最优方案选择：基于问题特点和项目整体架构选择最适合的方法
技术栈确定：明确使用的框架、库和工具，确保与项目技术栈一致
4. 程序设计文档
4.1 程序概述
核心功能和用途
在整个项目中的定位和作用
解决的主要问题
适用场景和应用领域
4.2 架构设计
整体结构图（用文字描述）
主要模块和组件关系
与现有程序的集成点
为后续程序预留的扩展点
数据流向和处理逻辑
4.3 核心功能规格
每个主要功能的详细描述
输入输出规格定义
关键算法或处理流程
与其他模块的交互方式
4.4 接口设计
对外接口：供其他模块调用的API
对内接口：调用其他模块的接口规范
预留接口：为未来扩展预留的接口设计
数据接口：数据模型和传输格式定义
配置参数和选项说明
5. 协作规范
代码规范：遵循项目统一的编码标准
文档规范：注释和文档的编写要求
版本控制：与其他模块的版本兼容性管理
测试协作：跨模块测试的协调机制
6. 测试策略
单元测试计划：核心函数的测试用例设计
集成测试方案：与现有模块的集成测试
接口测试：预留接口的可用性验证
边界条件测试：异常情况和极限值的处理验证
性能测试指标：关键性能指标的测试方法
7. 实现指南
开发环境配置要求
关键实现步骤和注意事项
代码结构和文件组织
错误处理和日志策略
与现有代码的集成方法
8. 使用说明
快速开始示例
常见使用场景的代码示例
与其他模块的集成示例
接口调用示例
最佳实践建议
重要提醒：

文档要精简但完整，避免冗余描述
特别关注模块间的协作和接口设计
必须考虑现有程序的兼容性和后续程序的扩展性
预留接口要有明确的设计意图和使用场景
在编写代码前，必须完成这份测试文档
文档完成后，严格按照文档进行编程实现